<template>
  <div id="profilePage">
    <div class="profile-container">
      <div class="profile-header">
        <h2 class="page-title">个人信息</h2>
        <div class="header-desc">查看和编辑您的个人信息</div>
      </div>

      <a-spin :spinning="loading">
        <a-form
          :model="formState"
          name="profileForm"
          autocomplete="off"
          @finish="handleSubmit"
          class="profile-form"
          v-if="formState.id"
        >
          <a-form-item label="用户ID" name="id">
            <a-input v-model:value="formState.id" disabled />
          </a-form-item>

          <a-form-item label="账号" name="userAccount">
            <a-input v-model:value="formState.userAccount" disabled />
          </a-form-item>

          <a-form-item
            label="用户名"
            name="userName"
            :rules="[{ required: true, message: '请输入用户名' }]"
          >
            <a-input v-model:value="formState.userName" placeholder="请输入用户名" />
          </a-form-item>

          <a-form-item label="头像" name="userAvatar">
            <a-input v-model:value="formState.userAvatar" placeholder="请输入头像链接" />
          </a-form-item>

          <a-form-item label="个人简介" name="userProfile">
            <a-textarea
              v-model:value="formState.userProfile"
              placeholder="请输入个人简介"
              :rows="4"
            />
          </a-form-item>

          <a-form-item label="用户角色" name="userRole">
            <a-input v-model:value="formState.userRole" disabled />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" html-type="submit" :loading="submitting">
              保存
            </a-button>
          </a-form-item>
        </a-form>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useLoginUserStore } from '@/stores/loginUser.ts'
import { getLoginUser, updateUser } from '@/api/userController.ts'

const loading = ref(false)
const submitting = ref(false)
const loginUserStore = useLoginUserStore()

const formState = reactive<API.UserUpdateRequest>({
  id: undefined,
  userName: '',
  userAvatar: '',
  userProfile: '',
  userRole: '',
})

const getInitialsAvatar = (name: string, account: string) => {
  if (!name && !account) return '';
  const initials = (name || account).substring(0, 2).toUpperCase();
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=random&color=fff`;
};

const fetchUserData = async () => {
  loading.value = true;
  try {
    const res = await getLoginUser();
    if (res.data.code === 0 && res.data.data) {
      const userData = res.data.data;
      formState.id = userData.id;
      formState.userAccount = userData.userAccount || ''; // 确保获取并设置 userAccount
      formState.userName = userData.userName || '';
      formState.userAvatar = userData.userAvatar || '';
      formState.userProfile = userData.userProfile || '';
      formState.userRole = userData.userRole || '';
    } else {
      message.error('获取用户信息失败: ' + res.data.message);
    }
  } catch (err) {
    message.error('获取用户信息失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async (values: any) => {
  submitting.value = true
  try {
    const res = await updateUser(values)
    if (res.data.code === 0) {
      message.success('保存成功')
      // 更新全局状态
      await loginUserStore.fetchLoginUser()
      // 重新获取用户信息
      await fetchUserData()
    } else {
      message.error('保存失败: ' + res.data.message)
    }
  } catch (err) {
    message.error('保存失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  fetchUserData()
})
</script>

<style scoped>
#profilePage {
  padding: 24px;
  min-height: calc(100vh - 64px - 74px);
}

.profile-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: 24px;
}

.page-title {
  color: #0277bd;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.header-desc {
  color: #666;
  font-size: 14px;
}

.profile-form :deep(.ant-form-item-label) {
  color: #0277bd;
  font-weight: 500;
}

@media (max-width: 768px) {
  #profilePage {
    padding: 16px;
  }

  .profile-container {
    padding: 16px;
  }
}
</style>