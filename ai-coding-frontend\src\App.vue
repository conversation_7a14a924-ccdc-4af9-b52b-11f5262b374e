<script setup lang="ts">
import BasicLayout from './layouts/BasicLayout.vue';
import GlobalHeader from './components/GlobalHeader.vue';
import GlobalFooter from './components/GlobalFooter.vue';

import '@/access'


</script>

<template>
  <div class="content-area">
    <GlobalHeader />
    <!-- 主要内容区域 -->
    <main>
      <router-view></router-view> <!-- 显示路由组件的内容 -->
    </main>
    <GlobalFooter />
  </div>
</template>

<style>
html, body, #app {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(45deg, #caaafa 0%, #c8e6f5 50%, #bbdefb 100%);
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  font-family: 'Arial', sans-serif;
  color: #333;
}

/* 新增：为内容区域添加类名，避免渐变背景影响页脚 */
#app .content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: transparent !important;
}
</style>
