<template>
  <div class="app-card">
    <div class="card-header">
      <div class="app-info">
        <h3 class="app-name">{{ app.appName || '未命名应用' }}</h3>
        <p class="app-description">{{ app.initPrompt || '暂无描述' }}</p>
      </div>
      <div class="app-meta">
        <span class="create-time">{{ formatTime(app.createTime) }}</span>
        <span v-if="showAuthor && app.user" class="author">
          by {{ app.user.userName }}
        </span>
      </div>
    </div>
    
    <div class="card-cover" v-if="app.cover">
      <img :src="app.cover" :alt="app.appName" />
    </div>
    
    <div class="card-actions">
      <a-button 
        v-if="app.deployKey" 
        type="primary" 
        @click="handleViewWork"
        class="action-button work-button"
      >
        查看作品
      </a-button>
      
      <a-button 
        @click="handleViewChat"
        class="action-button chat-button"
        :type="app.deployKey ? 'default' : 'primary'"
      >
        查看对话
      </a-button>
    </div>
    
    <div class="card-footer" v-if="app.deployedTime">
      <span class="deployed-time">
        <CheckCircleOutlined class="deployed-icon" />
        已部署于 {{ formatTime(app.deployedTime) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircleOutlined } from '@ant-design/icons-vue'

interface Props {
  app: API.AppVO
  showAuthor?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAuthor: true
})

const emit = defineEmits<{
  viewChat: [app: API.AppVO]
  viewWork: [app: API.AppVO]
}>()

const handleViewChat = () => {
  emit('viewChat', props.app)
}

const handleViewWork = () => {
  emit('viewWork', props.app)
}

const formatTime = (time?: string) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 30) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.app-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.app-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  padding: 20px;
}

.app-info {
  margin-bottom: 12px;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.app-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.app-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.create-time {
  color: #999;
}

.author {
  color: #0277bd;
  font-weight: 500;
}

.card-cover {
  height: 160px;
  overflow: hidden;
  background: #f5f5f5;
}

.card-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.app-card:hover .card-cover img {
  transform: scale(1.05);
}

.card-actions {
  padding: 16px 20px;
  display: flex;
  gap: 12px;
  background: #fafafa;
}

.action-button {
  flex: 1;
  border-radius: 6px;
  font-weight: 500;
}

.work-button {
  background: #333;
  border-color: #333;
  color: white;
}

.work-button:hover {
  background: #555;
  border-color: #555;
}

.chat-button {
  border-color: #0277bd;
  color: #0277bd;
}

.chat-button:hover {
  border-color: #0288d1;
  color: #0288d1;
}

.chat-button.ant-btn-primary {
  background: #0277bd;
  border-color: #0277bd;
}

.chat-button.ant-btn-primary:hover {
  background: #0288d1;
  border-color: #0288d1;
}

.card-footer {
  padding: 12px 20px;
  background: #f0f9ff;
  border-top: 1px solid #e6f7ff;
}

.deployed-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #0277bd;
}

.deployed-icon {
  color: #52c41a;
}

@media (max-width: 768px) {
  .card-header {
    padding: 16px;
  }
  
  .card-actions {
    padding: 12px 16px;
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
  
  .app-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
