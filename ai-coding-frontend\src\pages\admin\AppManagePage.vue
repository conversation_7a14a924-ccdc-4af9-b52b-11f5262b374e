<template>
  <div class="app-manage-page">
    <div class="page-header">
      <h1>应用管理</h1>
      <p>管理所有用户创建的应用</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="应用名称">
          <a-input 
            v-model:value="searchForm.appName" 
            placeholder="请输入应用名称"
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="创建者">
          <a-input 
            v-model:value="searchForm.userName" 
            placeholder="请输入创建者用户名"
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="优先级">
          <a-select 
            v-model:value="searchForm.priority" 
            placeholder="请选择优先级"
            style="width: 120px"
            allowClear
          >
            <a-select-option :value="99">精选</a-select-option>
            <a-select-option :value="0">普通</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            搜索
          </a-button>
          <a-button @click="handleReset" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 应用列表 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="apps"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'appName'">
            <div class="app-name-cell">
              <img v-if="record.cover" :src="record.cover" class="app-cover" />
              <div>
                <div class="app-name">{{ record.appName || '未命名应用' }}</div>
                <div class="app-prompt">{{ record.initPrompt }}</div>
              </div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'user'">
            <div class="user-cell">
              <a-avatar :src="record.user?.userAvatar" size="small">
                {{ record.user?.userName?.charAt(0) }}
              </a-avatar>
              <span class="user-name">{{ record.user?.userName }}</span>
            </div>
          </template>
          
          <template v-else-if="column.key === 'priority'">
            <a-tag :color="record.priority === 99 ? 'gold' : 'default'">
              {{ record.priority === 99 ? '精选' : '普通' }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'deployStatus'">
            <a-tag v-if="record.deployKey" color="green">已部署</a-tag>
            <a-tag v-else color="default">未部署</a-tag>
          </template>
          
          <template v-else-if="column.key === 'createTime'">
            {{ formatTime(record.createTime) }}
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleToggleFeatured(record)"
                :style="{ color: record.priority === 99 ? '#faad14' : '#1890ff' }"
              >
                {{ record.priority === 99 ? '取消精选' : '设为精选' }}
              </a-button>
              
              <a-popconfirm
                title="确定要删除这个应用吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { 
  listAppVoByPageByAdmin, 
  deleteAppByAdmin, 
  updateAppByAdmin 
} from '@/api/appController'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  appName: '',
  userName: '',
  priority: undefined as number | undefined
})

// 应用列表
const apps = ref<API.AppVO[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns = [
  {
    title: '应用信息',
    key: 'appName',
    width: 300
  },
  {
    title: '创建者',
    key: 'user',
    width: 120
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100
  },
  {
    title: '部署状态',
    key: 'deployStatus',
    width: 100
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const
  }
]

// 加载应用列表
const loadApps = async () => {
  loading.value = true
  try {
    const res = await listAppVoByPageByAdmin({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      appName: searchForm.appName || undefined,
      priority: searchForm.priority,
      // 这里需要根据用户名搜索，但API可能不支持，需要后端调整
    })
    
    if (res.data.code === 0 && res.data.data) {
      apps.value = res.data.data.records || []
      pagination.total = res.data.data.totalRow || 0
    } else {
      message.error('加载应用列表失败')
    }
  } catch (error) {
    message.error('加载应用列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadApps()
}

// 重置
const handleReset = () => {
  searchForm.appName = ''
  searchForm.userName = ''
  searchForm.priority = undefined
  pagination.current = 1
  loadApps()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadApps()
}

// 编辑应用
const handleEdit = (app: API.AppVO) => {
  router.push(`/app/edit/${app.id}`)
}

// 切换精选状态
const handleToggleFeatured = async (app: API.AppVO) => {
  const newPriority = app.priority === 99 ? 0 : 99
  
  try {
    const res = await updateAppByAdmin({
      id: app.id,
      priority: newPriority
    })
    
    if (res.data.code === 0) {
      message.success(newPriority === 99 ? '设为精选成功' : '取消精选成功')
      loadApps()
    } else {
      message.error('操作失败：' + res.data.message)
    }
  } catch (error) {
    message.error('操作失败，请稍后重试')
  }
}

// 删除应用
const handleDelete = async (app: API.AppVO) => {
  try {
    const res = await deleteAppByAdmin({ id: app.id })
    
    if (res.data.code === 0) {
      message.success('删除成功')
      loadApps()
    } else {
      message.error('删除失败：' + res.data.message)
    }
  } catch (error) {
    message.error('删除失败，请稍后重试')
  }
}

// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return ''
  return new Date(time).toLocaleDateString()
}

onMounted(() => {
  loadApps()
})
</script>

<style scoped>
.app-manage-page {
  padding: 24px;
  background: white;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #666;
  margin: 0;
}

.search-section {
  background: #fafafa;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.table-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.app-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-cover {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.app-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.app-prompt {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-width: 200px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  color: #333;
  font-size: 14px;
}

:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-pagination) {
  margin-top: 24px;
  text-align: right;
}

:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
}

:deep(.ant-btn-link:hover) {
  background: none;
}

@media (max-width: 768px) {
  .app-manage-page {
    padding: 16px;
  }

  .search-section {
    padding: 16px;
  }

  :deep(.ant-form-inline .ant-form-item) {
    margin-bottom: 16px;
  }

  .app-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .app-prompt {
    max-width: none;
  }
}
</style>
