<template>
  <a-layout class="layout-container">
    <GlobalHeader />
    <a-layout-content class="layout-content">
      <div class="content-wrapper">
        <router-view />
      </div>
    </a-layout-content>
    <GlobalFooter />
  </a-layout>
</template>

<script setup lang="ts">
import GlobalHeader from '@/components/GlobalHeader.vue';
import GlobalFooter from '@/components/GlobalFooter.vue';
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
}

.layout-content {
  padding: 0;
  background: #f0f2f5;
}

.content-wrapper {
  padding: 24px;
  min-height: calc(100vh - 64px - 52px); /* 减去header和footer的高度 */
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 16px;
  }
}
</style>
