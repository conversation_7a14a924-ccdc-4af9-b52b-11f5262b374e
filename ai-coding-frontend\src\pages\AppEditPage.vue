<template>
  <div class="app-edit-page">
    <div class="page-header">
      <a-button type="text" @click="goBack" class="back-button">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回
      </a-button>
      <h1>{{ isAdmin ? '编辑应用' : '修改应用信息' }}</h1>
    </div>

    <div class="edit-content" v-if="appInfo">
      <a-form
        :model="formData"
        :rules="rules"
        layout="vertical"
        @finish="handleSubmit"
        class="edit-form"
      >
        <a-row :gutter="24">
          <a-col :span="24" :md="12">
            <a-form-item label="应用名称" name="appName">
              <a-input 
                v-model:value="formData.appName" 
                placeholder="请输入应用名称"
                size="large"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="24" :md="12" v-if="isAdmin">
            <a-form-item label="优先级" name="priority">
              <a-select 
                v-model:value="formData.priority" 
                placeholder="请选择优先级"
                size="large"
              >
                <a-select-option :value="0">普通</a-select-option>
                <a-select-option :value="99">精选</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="应用描述" name="initPrompt">
          <a-textarea 
            v-model:value="formData.initPrompt" 
            placeholder="请输入应用描述"
            :rows="4"
            :disabled="true"
            class="disabled-textarea"
          />
          <div class="form-tip">应用描述不可修改</div>
        </a-form-item>

        <a-form-item label="应用封面" name="cover" v-if="isAdmin">
          <div class="cover-upload">
            <div class="current-cover" v-if="formData.cover">
              <img :src="formData.cover" alt="当前封面" class="cover-image" />
              <div class="cover-actions">
                <a-button type="link" @click="removeCover" danger>
                  删除封面
                </a-button>
              </div>
            </div>
            <a-input 
              v-model:value="formData.cover" 
              placeholder="请输入封面图片URL"
              size="large"
            />
            <div class="form-tip">请输入有效的图片URL地址</div>
          </div>
        </a-form-item>

        <div class="app-info-section">
          <h3>应用信息</h3>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="应用ID">
              {{ appInfo.id }}
            </a-descriptions-item>
            <a-descriptions-item label="创建者">
              <div class="user-info">
                <a-avatar :src="appInfo.user?.userAvatar" size="small">
                  {{ appInfo.user?.userName?.charAt(0) }}
                </a-avatar>
                <span>{{ appInfo.user?.userName }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(appInfo.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatTime(appInfo.updateTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="部署状态">
              <a-tag v-if="appInfo.deployKey" color="green">
                已部署
              </a-tag>
              <a-tag v-else color="default">
                未部署
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="部署时间" v-if="appInfo.deployedTime">
              {{ formatTime(appInfo.deployedTime) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="form-actions">
          <a-button @click="goBack" size="large">
            取消
          </a-button>
          <a-button 
            type="primary" 
            html-type="submit" 
            size="large"
            :loading="saving"
          >
            保存修改
          </a-button>
        </div>
      </a-form>
    </div>

    <div v-else class="loading-container">
      <a-spin size="large" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/loginUser'
import { 
  getAppVoById, 
  getAppVoByIdByAdmin,
  updateApp, 
  updateAppByAdmin 
} from '@/api/appController'

const route = useRoute()
const router = useRouter()
const loginUserStore = useLoginUserStore()

const appId = computed(() => Number(route.params.id))
const isAdmin = computed(() => loginUserStore.loginUser.userRole === 'admin')

// 应用信息
const appInfo = ref<API.AppVO>()
const saving = ref(false)

// 表单数据
const formData = reactive({
  appName: '',
  cover: '',
  priority: 0,
  initPrompt: ''
})

// 表单验证规则
const rules = {
  appName: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 1, max: 50, message: '应用名称长度在1-50个字符', trigger: 'blur' }
  ]
}

// 权限检查
const checkPermission = () => {
  if (!appInfo.value) return false
  
  // 管理员可以编辑所有应用
  if (isAdmin.value) return true
  
  // 普通用户只能编辑自己的应用
  return appInfo.value.userId === loginUserStore.loginUser.id
}

// 加载应用信息
const loadAppInfo = async () => {
  try {
    const res = isAdmin.value 
      ? await getAppVoByIdByAdmin({ id: appId.value })
      : await getAppVoById({ id: appId.value })
    
    if (res.data.code === 0 && res.data.data) {
      appInfo.value = res.data.data
      
      // 填充表单数据
      formData.appName = appInfo.value.appName || ''
      formData.cover = appInfo.value.cover || ''
      formData.priority = appInfo.value.priority || 0
      formData.initPrompt = appInfo.value.initPrompt || ''
      
      // 检查权限
      if (!checkPermission()) {
        message.error('您没有权限编辑此应用')
        router.push('/')
        return
      }
    } else {
      message.error('加载应用信息失败')
      router.push('/')
    }
  } catch (error) {
    message.error('加载应用信息失败')
    router.push('/')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!appInfo.value || !checkPermission()) {
    message.error('您没有权限编辑此应用')
    return
  }
  
  saving.value = true
  try {
    const updateData = {
      id: appInfo.value.id,
      appName: formData.appName
    }
    
    let res
    if (isAdmin.value) {
      // 管理员可以修改更多字段
      res = await updateAppByAdmin({
        ...updateData,
        cover: formData.cover || undefined,
        priority: formData.priority
      })
    } else {
      // 普通用户只能修改应用名称
      res = await updateApp(updateData)
    }
    
    if (res.data.code === 0) {
      message.success('保存成功')
      router.back()
    } else {
      message.error('保存失败：' + res.data.message)
    }
  } catch (error) {
    message.error('保存失败，请稍后重试')
  } finally {
    saving.value = false
  }
}

// 删除封面
const removeCover = () => {
  formData.cover = ''
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

onMounted(() => {
  loadAppInfo()
})
</script>

<style scoped>
.app-edit-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 24px;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.back-button {
  color: #666;
}

.back-button:hover {
  color: #0277bd;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.edit-content {
  max-width: 800px;
  margin: 0 auto;
}

.edit-form {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.disabled-textarea {
  background: #f5f5f5;
}

:deep(.disabled-textarea .ant-input) {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.cover-upload {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.current-cover {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
}

.cover-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.cover-actions {
  flex: 1;
}

.app-info-section {
  margin: 32px 0;
}

.app-info-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background: #fafafa;
  font-weight: 500;
}

:deep(.ant-btn-primary) {
  background: #0277bd;
  border-color: #0277bd;
}

:deep(.ant-btn-primary:hover) {
  background: #0288d1;
  border-color: #0288d1;
}

@media (max-width: 768px) {
  .app-edit-page {
    padding: 16px;
  }

  .edit-form {
    padding: 24px 16px;
  }

  .page-header {
    margin-bottom: 24px;
  }

  .page-header h1 {
    font-size: 20px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .form-actions .ant-btn {
    width: 100%;
  }

  .current-cover {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
