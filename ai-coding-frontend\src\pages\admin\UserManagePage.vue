<template>
  <div id="userManagePage">
    <div class="user-manage-container">
      <div class="header-section">
        <h2 class="page-title">用户管理</h2>
        <div class="header-desc">管理系统中的所有用户信息</div>
      </div>
      
      <!-- 搜索表单 -->
      <div class="search-section">
        <a-form layout="inline" :model="searchParams" @finish="doSearch" class="search-form">
          <a-form-item label="账号">
            <a-input v-model:value="searchParams.userAccount" placeholder="输入账号" />
          </a-form-item>
          <a-form-item label="用户名">
            <a-input v-model:value="searchParams.userName" placeholder="输入用户名" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">搜索</a-button>
          </a-form-item>
        </a-form>
      </div>
      
      <a-divider />
      
      <!-- 表格 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :data-source="data"
          :pagination="pagination"
          @change="doTableChange"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'userAvatar'">
              <a-avatar :src="record.userAvatar || getInitialsAvatar(record.userName, record.userAccount)" :size="38" />
            </template>
            <template v-else-if="column.dataIndex === 'userRole'">
              <div v-if="record.userRole === 'admin'">
                <a-tag color="green">管理员</a-tag>
              </div>
              <div v-else>
                <a-tag color="blue">普通用户</a-tag>
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'createTime'">
              {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template v-else-if="column.key === 'action'">
              <a-button 
                type="primary" 
                @click="editUser(record)" 
                :disabled="record.userRole === 'admin' && record.id !== loginUserStore.loginUser.id"
                style="margin-right: 8px;"
              >
                编辑
              </a-button>
              <a-button 
                danger 
                @click="doDelete(record.id, record.userRole)" 
                :disabled="record.id === loginUserStore.loginUser.id || record.userRole === 'admin'"
              >
                删除
              </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    
    <!-- 编辑用户模态框 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑用户"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      :confirm-loading="editSubmitting"
    >
      <a-form
        :model="editFormState"
        name="editUserForm"
        autocomplete="off"
        ref="editFormRef"
      >
        <a-form-item label="用户ID" name="id">
          <a-input v-model:value="editFormState.id" disabled />
        </a-form-item>
        
        <a-form-item label="账号" name="userAccount">
          <a-input v-model:value="editFormState.userAccount" disabled />
        </a-form-item>
        
        <a-form-item
          label="用户名"
          name="userName"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input v-model:value="editFormState.userName" placeholder="请输入用户名" />
        </a-form-item>
        
        <a-form-item label="头像" name="userAvatar">
          <a-input v-model:value="editFormState.userAvatar" placeholder="请输入头像链接" />
        </a-form-item>
        
        <a-form-item label="个人简介" name="userProfile">
          <a-textarea
            v-model:value="editFormState.userProfile"
            placeholder="请输入个人简介"
            :rows="4"
          />
        </a-form-item>
        
        <a-form-item label="用户角色" name="userRole">
          <a-select
            v-model:value="editFormState.userRole"
            placeholder="请选择用户角色"
            :options="roleOptions"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed } from 'vue'
import { deleteUser, listUserVoByPage, updateUser, getUserById } from '@/api/userController.ts'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { useLoginUserStore } from '@/stores/loginUser.ts'

const loginUserStore = useLoginUserStore()

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '账号',
    dataIndex: 'userAccount',
    width: 150,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    width: 150,
  },
  {
    title: '头像',
    dataIndex: 'userAvatar',
    width: 80,
  },
  {
    title: '简介',
    dataIndex: 'userProfile',
  },
  {
    title: '用户角色',
    dataIndex: 'userRole',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]

// 数据
const data = ref<API.UserVO[]>([])
const total = ref(0)

// 搜索条件
const searchParams = reactive<API.UserQueryRequest>({
  pageNum: 1,
  pageSize: 10,
})

// 编辑模态框相关
const editModalVisible = ref(false)
const editSubmitting = ref(false)
const editFormRef = ref()
const editFormState = reactive<API.UserUpdateRequest>({
  id: undefined,
  userName: '',
  userAvatar: '',
  userProfile: '',
  userRole: '',
})

const roleOptions = [
  { label: '普通用户', value: 'user' },
  { label: '管理员', value: 'admin' },
]

const getInitialsAvatar = (name: string, account: string) => {
  if (!name && !account) return '';
  const initials = (name || account).substring(0, 2).toUpperCase();
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=random&color=fff`;
};

// 获取数据
const fetchData = async () => {
  const res = await listUserVoByPage({
    ...searchParams,
  })
  if (res.data.data) {
    data.value = res.data.data.records ?? []
    total.value = res.data.data.totalRow ?? 0
  } else {
    message.error('获取数据失败，' + res.data.message)
  }
}

// 分页参数
const pagination = computed(() => {
  return {
    current: searchParams.pageNum ?? 1,
    pageSize: searchParams.pageSize ?? 10,
    total: total.value,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  }
})

// 表格变化处理
const doTableChange = (page: any) => {
  searchParams.pageNum = page.current
  searchParams.pageSize = page.pageSize
  fetchData()
}

// 获取数据
const doSearch = () => {
  // 重置页码
  searchParams.pageNum = 1
  fetchData()
}

// 编辑用户
const editUser = async (record: API.UserVO) => {
  // 检查权限：只能编辑当前用户或普通用户
  if (record.userRole === 'admin' && record.id !== loginUserStore.loginUser.id) {
    message.warning('您只能编辑自己的信息或普通用户信息')
    return
  }
  
  // 获取完整用户信息
  try {
    const res = await getUserById({ id: record.id as number })
    if (res.data.code === 0 && res.data.data) {
      const userData = res.data.data
      editFormState.id = userData.id
      editFormState.userName = userData.userName || ''
      editFormState.userAvatar = userData.userAvatar || ''
      editFormState.userProfile = userData.userProfile || ''
      editFormState.userRole = userData.userRole || 'user'
      // 确保 userAccount 被正确赋值
      editFormState.userAccount = userData.userAccount || ''
      editModalVisible.value = true
    } else {
      message.error('获取用户信息失败: ' + res.data.message)
    }
  } catch (err) {
    message.error('获取用户信息失败，请稍后重试')
  }
}

// 处理编辑确认
const handleEditOk = async () => {
  try {
    await editFormRef.value?.validateFields()
    editSubmitting.value = true
    
    const res = await updateUser(editFormState)
    if (res.data.code === 0) {
      message.success('更新成功')
      editModalVisible.value = false
      // 重置表单
      editFormRef.value?.resetFields()
      // 刷新数据
      await fetchData()
      
      // 如果编辑的是当前用户，更新全局状态
      if (editFormState.id === loginUserStore.loginUser.id) {
        await loginUserStore.fetchLoginUser()
      }
    } else {
      message.error('更新失败: ' + res.data.message)
    }
  } catch (err: any) {
    if (err?.errorFields) {
      // 表单验证错误，已在表单中显示
    } else {
      message.error('更新失败，请稍后重试')
    }
  } finally {
    editSubmitting.value = false
  }
}

// 处理编辑取消
const handleEditCancel = () => {
  editModalVisible.value = false
  editFormRef.value?.resetFields()
}

// 删除数据
const doDelete = async (id: any, userRole: string) => {
  if (!id) {
    return
  }
  
  // 检查是否是当前登录用户
  if (id === loginUserStore.loginUser.id) {
    message.warning('不能删除当前登录用户')
    return
  }
  
  // 检查是否是管理员用户
  if (userRole === 'admin') {
    message.warning('不能删除管理员用户')
    return
  }
  
  // 确认删除
  const confirmed = await new Promise((resolve) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个用户吗？此操作不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: () => resolve(true),
      onCancel: () => resolve(false),
    })
  })
  
  if (!confirmed) return
  
  const res = await deleteUser({ id })
  if (res.data.code === 0) {
    message.success('删除成功')
    // 刷新数据
    fetchData()
  } else {
    message.error('删除失败: ' + res.data.message)
  }
}

// 页面加载时请求一次
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
#userManagePage {
  padding: 24px;
  min-height: calc(100vh - 64px - 74px);
}

.user-manage-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header-section {
  margin-bottom: 24px;
}

.page-title {
  color: #0277bd;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.header-desc {
  color: #666;
  font-size: 14px;
}

.search-section {
  background: #f5f8fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 24px;
}

.search-form :deep(.ant-form-item-label) {
  color: #0277bd;
  font-weight: 500;
}

.table-section :deep(.ant-table-thead > tr > th) {
  background-color: #e6f7ff;
  color: #0277bd;
  font-weight: 600;
}

.table-section :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f0faff;
}

@media (max-width: 768px) {
  #userManagePage {
    padding: 16px;
  }
  
  .user-manage-container {
    padding: 16px;
  }
  
  .search-section {
    padding: 12px;
  }
}
</style>