<template>
  <div id="UserLoginPage">
    <div class="login-container">
      <div class="login-header">
        <img src="/logo.png" alt="Logo" class="logo" />
        <h2 class="title">AI零代码应用生成</h2>
        <div class="desc">不写一行代码，生成完整应用</div>
      </div>

      <a-form
        :model="formState"
        name="basic"
        autocomplete="off"
        @finish="handlerSubmit"
        class="login-form"
      >
        <a-form-item
          name="userAccount"
          :rules="[
            { required: true, message: '请输入账号' },
            { min: 4, message: '账号长度不能小于4' },
          ]"
        >
          <a-input v-model:value="formState.userAccount" placeholder="请输入账号" size="large">
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item
          name="userPassword"
          :rules="[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码长度不能小于6' },
          ]"
        >
          <a-input-password
            v-model:value="formState.userPassword"
            placeholder="请输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <div class="tips">
          没有账号？
          <RouterLink to="/user/register">去注册</RouterLink>
        </div>

        <a-form-item>
          <a-button type="primary" html-type="submit" block size="large" :loading="loading">
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useLoginUserStore } from '@/stores/loginUser.ts'
import { message } from 'ant-design-vue'
import { login } from '@/api/userController.ts'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'

const formState = reactive<API.UserLoginRequest>({
  userAccount: '',
  userPassword: '',
})

const loading = ref(false)
const router = useRouter()
const loginUserStore = useLoginUserStore()

/**
 * 提交表单
 * @param values
 */
const handlerSubmit = async (values: any) => {
  loading.value = true
  try {
    const res = await login(values)
    // 登录成功，把登录态保存到全局状态中
    if (res.data.code === 0 && res.data.data) {
      await loginUserStore.fetchLoginUser()
      message.success('登录成功')
      router.push({
        path: '/',
        replace: true,
      })
    } else {
      message.error('登录失败，' + res.data.message)
    }
  } catch (err) {
    message.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
#UserLoginPage {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 64px - 74px); /* 减去header和footer的高度 */
  padding: 24px 0;
}

.login-container {
  width: 100%;
  max-width: 400px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.title {
  color: #0277bd;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.desc {
  color: #666;
  font-size: 14px;
}

.login-form {
  margin-top: 24px;
}

.tips {
  margin-bottom: 16px;
  color: #666;
  font-size: 13px;
  text-align: right;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 6px;
}

:deep(.ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused) {
  border-color: #0277bd;
  box-shadow: 0 0 0 2px rgba(2, 119, 189, 0.2);
}

:deep(.ant-btn-primary) {
  background: #0277bd;
  border-color: #0277bd;
}

:deep(.ant-btn-primary:hover) {
  background: #0288d1;
  border-color: #0288d1;
}

@media (max-width: 768px) {
  .login-container {
    margin: 0 16px;
    padding: 24px;
  }

  #UserLoginPage {
    min-height: calc(100vh - 64px);
  }
}
</style>
