<template>
  <div class="app-chat-page">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <div class="app-info">
        <a-button type="text" @click="goBack" class="back-button">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
        </a-button>
        <h1 class="app-name">{{ appInfo?.appName || '应用名称' }}</h1>
      </div>
      
      <div class="top-actions">
        <a-button 
          v-if="canEdit"
          type="primary" 
          @click="handleDeploy"
          :loading="deploying"
          class="deploy-button"
        >
          部署按钮
        </a-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧对话区域 -->
      <div class="chat-section">
        <div class="messages-container" ref="messagesContainer">
          <div 
            v-for="message in messages" 
            :key="message.id"
            :class="['message', message.messageType === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-avatar">
              <a-avatar v-if="message.messageType === 'user'" :src="loginUserStore.loginUser.userAvatar">
                {{ loginUserStore.loginUser.userName?.charAt(0) }}
              </a-avatar>
              <a-avatar v-else style="background-color: #0277bd;">
                AI
              </a-avatar>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.message)"></div>
              <div class="message-time">{{ formatTime(message.createTime) }}</div>
            </div>
          </div>
          
          <!-- AI 正在输入 -->
          <div v-if="isAiTyping" class="message ai-message">
            <div class="message-avatar">
              <a-avatar style="background-color: #0277bd;">AI</a-avatar>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="input-section">
          <div v-if="!canEdit" class="input-disabled-overlay">
            <span>无法在别人的作品下对话哦~</span>
          </div>
          <a-input
            v-model:value="inputMessage"
            placeholder="请输入消息..."
            :disabled="!canEdit || sending"
            @pressEnter="handleSendMessage"
            class="message-input"
          >
            <template #suffix>
              <a-button 
                type="primary" 
                :disabled="!canEdit || !inputMessage.trim() || sending"
                @click="handleSendMessage"
                :loading="sending"
                class="send-button"
              >
                发送
              </a-button>
            </template>
          </a-input>
        </div>
      </div>

      <!-- 右侧网页展示区域 -->
      <div class="preview-section">
        <div class="preview-header">
          <h3>生成后的网页展示</h3>
          <a-button 
            v-if="previewUrl" 
            type="link" 
            @click="openInNewTab"
            class="open-link"
          >
            <template #icon>
              <ExportOutlined />
            </template>
            新窗口打开
          </a-button>
        </div>
        
        <div class="preview-content">
          <iframe 
            v-if="previewUrl"
            :src="previewUrl"
            class="preview-iframe"
            frameborder="0"
          ></iframe>
          <div v-else class="preview-placeholder">
            <div class="placeholder-content">
              <FileTextOutlined class="placeholder-icon" />
              <p>网站文件生成完成后将在此展示</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { 
  ArrowLeftOutlined, 
  ExportOutlined, 
  FileTextOutlined 
} from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/loginUser'
import { getAppVoById, deployApp } from '@/api/appController'

const route = useRoute()
const router = useRouter()
const loginUserStore = useLoginUserStore()

// 应用信息
const appInfo = ref<API.AppVO>()
const appId = computed(() => route.params.id as string)
const isViewMode = computed(() => route.query.view === '1')

// 权限控制
const canEdit = computed(() => {
  if (!appInfo.value || !loginUserStore.loginUser.id) return false
  return appInfo.value.userId === loginUserStore.loginUser.id
})

// 是否可以查看（所有人都可以查看，但只有作者可以编辑）
const canView = computed(() => {
  return !!appInfo.value
})

// 对话相关
const messages = ref<any[]>([])
const inputMessage = ref('')
const sending = ref(false)
const isAiTyping = ref(false)
const messagesContainer = ref<HTMLElement>()

// 预览相关
const previewUrl = ref('')
const deploying = ref(false)

// 返回上一页
const goBack = () => {
  router.back()
}

// 加载应用信息
const loadAppInfo = async (retryCount = 0) => {
  try {
    const res = await getAppVoById({ id: appId.value })
    if (res.data.code === 0 && res.data.data) {
      appInfo.value = res.data.data

      // 设置预览URL
      if (appInfo.value.codeGenType && appInfo.value.id) {
        previewUrl.value = `http://localhost:8123/api/static/${appInfo.value.codeGenType}_${appInfo.value.id}/`
      }

      // 如果不是查看模式且是自己的应用，自动发送初始消息
      if (!isViewMode.value && canEdit.value && appInfo.value.initPrompt) {
        await sendInitialMessage()
      }
    } else {
      // 如果是新创建的应用，可能需要重试
      if (retryCount < 3) {
        console.log(`应用信息加载失败，正在重试... (${retryCount + 1}/3)`)
        setTimeout(() => {
          loadAppInfo(retryCount + 1)
        }, 1000)
        return
      }
      message.error('加载应用信息失败：' + res.data.message)
      router.push('/')
    }
  } catch (error) {
    // 如果是新创建的应用，可能需要重试
    if (retryCount < 3) {
      console.log(`应用信息加载失败，正在重试... (${retryCount + 1}/3)`)
      setTimeout(() => {
        loadAppInfo(retryCount + 1)
      }, 1000)
      return
    }
    console.error('加载应用信息失败:', error)
    message.error('加载应用信息失败，请稍后重试')
    router.push('/')
  }
}

// 发送初始消息
const sendInitialMessage = async () => {
  if (!appInfo.value?.initPrompt) return
  
  // 添加用户消息
  const userMessage = {
    id: Date.now(),
    message: appInfo.value.initPrompt,
    messageType: 'user',
    createTime: new Date().toISOString()
  }
  messages.value.push(userMessage)
  
  // 开始AI对话
  await startAiChat(appInfo.value.initPrompt)
}

// 发送消息
const handleSendMessage = async () => {
  if (!inputMessage.value.trim() || sending.value || !canEdit.value) return
  
  const message = inputMessage.value.trim()
  inputMessage.value = ''
  
  // 添加用户消息
  const userMessage = {
    id: Date.now(),
    message,
    messageType: 'user',
    createTime: new Date().toISOString()
  }
  messages.value.push(userMessage)
  
  await startAiChat(message)
}

// 开始AI对话
const startAiChat = async (message: string) => {
  sending.value = true
  isAiTyping.value = true

  try {
    // 创建AI消息占位符
    const aiMessage = {
      id: Date.now() + 1,
      message: '',
      messageType: 'ai',
      createTime: new Date().toISOString()
    }
    messages.value.push(aiMessage)

    // 使用SSE连接
    const eventSource = new EventSource(
      `http://localhost:8123/api/app/chat/gen/code?appId=${appId.value}&message=${encodeURIComponent(message)}`
    )

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)

        if (data.type === 'message') {
          // 更新AI消息内容
          const lastMessage = messages.value[messages.value.length - 1]
          if (lastMessage && lastMessage.messageType === 'ai') {
            lastMessage.message += data.content
            scrollToBottom()
          }
        } else if (data.type === 'done') {
          // 对话完成
          eventSource.close()
          sending.value = false
          isAiTyping.value = false

          // 更新预览URL
          if (appInfo.value) {
            previewUrl.value = `http://localhost:8123/api/static/${appInfo.value.codeGenType || 'html'}_${appInfo.value.id}/`
          }
        } else if (data.type === 'error') {
          // 处理错误
          eventSource.close()
          message.error('生成失败：' + data.message)
          sending.value = false
          isAiTyping.value = false
        }
      } catch (e) {
        // 如果不是JSON格式，直接添加到消息中
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.messageType === 'ai') {
          lastMessage.message += event.data
          scrollToBottom()
        }
      }
    }

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error)
      eventSource.close()
      message.error('连接失败，请稍后重试')
      sending.value = false
      isAiTyping.value = false
    }

    // 设置超时
    setTimeout(() => {
      if (eventSource.readyState !== EventSource.CLOSED) {
        eventSource.close()
        message.error('请求超时，请稍后重试')
        sending.value = false
        isAiTyping.value = false
      }
    }, 300000) // 5分钟超时

  } catch (error) {
    console.error('发送消息失败:', error)
    message.error('发送消息失败')
    sending.value = false
    isAiTyping.value = false
  }
}

// 部署应用
const handleDeploy = async () => {
  if (!appInfo.value?.id) return
  
  deploying.value = true
  try {
    const res = await deployApp({ appId: appInfo.value.id })
    if (res.data.code === 0) {
      message.success('部署成功！')
      // 重新加载应用信息以获取部署信息
      await loadAppInfo()
    } else {
      message.error('部署失败：' + res.data.message)
    }
  } catch (error) {
    message.error('部署失败，请稍后重试')
  } finally {
    deploying.value = false
  }
}

// 在新窗口打开预览
const openInNewTab = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank')
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 格式化消息
const formatMessage = (msg: string) => {
  return msg.replace(/\n/g, '<br>')
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleTimeString()
}

// 监听消息变化，自动滚动
watch(messages, () => {
  scrollToBottom()
}, { deep: true })

onMounted(() => {
  loadAppInfo()
})
</script>

<style scoped>
.app-chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.app-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-button {
  color: #666;
}

.back-button:hover {
  color: #0277bd;
}

.app-name {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.deploy-button {
  background: #0277bd;
  border-color: #0277bd;
}

.deploy-button:hover {
  background: #0288d1;
  border-color: #0288d1;
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.chat-section {
  width: 50%;
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e8e8e8;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.user-message .message-content {
  text-align: right;
}

.message-text {
  background: #f0f0f0;
  padding: 12px 16px;
  border-radius: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message .message-text {
  background: #0277bd;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: #f0f0f0;
  border-radius: 12px;
  width: fit-content;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-section {
  position: relative;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  background: white;
}

.input-disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  color: #999;
  font-size: 14px;
}

.message-input {
  border-radius: 24px;
}

:deep(.message-input .ant-input) {
  border-radius: 24px;
  padding: 12px 20px;
}

.send-button {
  background: #0277bd;
  border-color: #0277bd;
  border-radius: 20px;
  margin-right: 4px;
}

.send-button:hover {
  background: #0288d1;
  border-color: #0288d1;
}

.preview-section {
  width: 50%;
  display: flex;
  flex-direction: column;
  background: white;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.open-link {
  color: #0277bd;
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.preview-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.placeholder-content {
  text-align: center;
  color: #999;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

.placeholder-content p {
  margin: 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .chat-section,
  .preview-section {
    width: 100%;
    height: 50%;
  }

  .top-bar {
    padding: 12px 16px;
  }

  .app-name {
    font-size: 18px;
  }

  .messages-container {
    padding: 16px;
  }

  .input-section {
    padding: 12px 16px;
  }

  .preview-header {
    padding: 12px 16px;
  }
}
</style>
