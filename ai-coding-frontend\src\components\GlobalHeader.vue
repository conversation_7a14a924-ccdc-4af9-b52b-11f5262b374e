<template>
  <a-layout-header class="global-header">
    <div class="logo-container">
      <img src="/logo.png" alt="Logo" class="logo" />
      <h1 class="site-title">AI零代码开发平台</h1>
    </div>
    <a-menu
      v-model:selectedKeys="selectedKeys"
      theme="light"
      mode="horizontal"
      :style="{ lineHeight: '64px', flex: 1 }"
      class="custom-menu"
      @click="handleMenuClick"
    >
      <a-menu-item v-for="item in menuItems" :key="item.key">
        <template #icon v-if="item.icon">
          <component :is="item.icon" />
        </template>
        {{ item.title }}
      </a-menu-item>
    </a-menu>

    <div class="user-info">
      <div v-if="loginUserStore.loginUser.id">
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile">
                <RouterLink to="/user/profile">个人信息</RouterLink>
              </a-menu-item>
              <a-menu-item key="logout" @click="handleLogout">
                <span>注销</span>
              </a-menu-item>
            </a-menu>
          </template>
          <div class="user-dropdown">
            <a-avatar :src="loginUserStore.loginUser.userAvatar" />
            <span class="username">{{ loginUserStore.loginUser.userName }}</span>
          </div>
        </a-dropdown>
      </div>
      <div v-else>
        <a-button type="primary" v-if="!isLoggedIn" @click="login">
          登录
        </a-button>
      </div>
    </div>
  </a-layout-header>
</template>

<script setup lang="ts">
// HTML 展示数据

// JS 中引入 Store
import { useLoginUserStore } from '@/stores/loginUser.ts'
const loginUserStore = useLoginUserStore()

import { ref, markRaw, watch, computed, h } from 'vue'
import {
  HomeOutlined,
  ProjectOutlined,
  AppstoreOutlined,
  ReadOutlined
} from '@ant-design/icons-vue';
import { logout } from '@/api/userController.ts'
import { message } from 'ant-design-vue'
import { useRouter } from "vue-router";

import { useRoute } from "vue-router";
import type { MenuProps } from 'ant-design-vue'
const router = useRouter();
const route = useRoute();

// 处理菜单点击
const handleMenuClick: MenuProps['onClick'] = (e) => {
  const key = e.key as string
  selectedKeys.value = [key]
  // 跳转到对应页面
  const menuItem = menuItems.value.find(item => item.key === key);
  if (menuItem && (menuItem as any).path) {
    router.push((menuItem as any).path);
  }
}

// 处理注销
const handleLogout = async () => {
  const res = await logout()
  if (res.data.code === 0) {
    // 清空登录用户信息
    loginUserStore.setLoginUser({ userName: '未登录' })
    message.success('注销成功')
    // 跳转到首页
    router.push('/')
  } else {
    message.error('注销失败: ' + res.data.message)
  }
}

// 菜单配置项
const originItems = [
  {
    key: '/',
    icon: markRaw(HomeOutlined),
    label: '主页',
    title: '主页',
    path: '/'
  },
  {
    key: '/admin/userManage',
    label: '用户管理',
    title: '用户管理',
    path: '/admin/userManage'
  }
]

// 过滤菜单项
const filterMenus = (menus: any[]) => {
  return menus?.filter((menu) => {
    if (menu.key === '/admin/userManage') {
      // 只有已登录且角色为admin的用户才能看到用户管理菜单
      const loginUser = loginUserStore.loginUser
      if (!loginUser || !loginUser.id || loginUser.userRole !== 'admin') {
        return false
      }
    }
    return true
  })
}

// 展示在菜单的路由数组
const menuItems = computed<MenuProps['items']>(() => filterMenus(originItems))

// 当前选中菜单
const selectedKeys = ref<string[]>(['/']);

// 监听路由变化，更新当前选中菜单
watch(
  () => route.path,
  (newPath) => {
    // 精确匹配
    if (menuItems.value.some((item: any) => item.path === newPath)) {
      selectedKeys.value = [newPath];
    } else {
      // 默认选中首页
      selectedKeys.value = ['/'];
    }
  },
  { immediate: true }
);

const isLoggedIn = ref(false);
const username = ref('用户名');
const userAvatar = ref('https://joeschmoe.io/api/v1/random');

const login = () => {
  router.push('/user/login')
};
</script>

<style scoped>
.global-header {
  display: flex;
  align-items: center;
  padding: 0 24px;
  color: #0277bd;
  height: 64px;
  box-shadow: none;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-right: 32px;
}

.logo {
  height: 32px;
  margin-right: 12px;
}

.site-title {
  color: #0277bd;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  white-space: nowrap;
}

.user-info {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 8px;
}

.username {
  color: #0277bd;
  margin-left: 8px;
  font-weight: 600;
  font-size: 16px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* 自定义菜单样式 */
:deep(.custom-menu) {
  background: transparent !important;
  border-bottom: none !important;
}

:deep(.custom-menu .ant-menu-item) {
  margin: 0 8px;
  padding: 0 24px;
  font-size: 15px;
  font-weight: 600;
  color: #0277bd !important;
  border-radius: 4px;
  transition: all 0.3s ease;
}

:deep(.custom-menu .ant-menu-item:hover) {
  color: #0277bd !important;
  background-color: rgba(2, 119, 189, 0.1) !important;
}

:deep(.custom-menu .ant-menu-item-selected) {
  color: #0277bd !important;
  font-weight: 600;
  background-color: rgba(2, 119, 189, 0.15) !important;
}

:deep(.custom-menu .ant-menu-item .anticon) {
  margin-right: 8px;
  font-size: 16px;
  color: inherit !important;
}

:deep(.custom-menu .ant-menu-item::after) {
  display: none;
}

:deep(.ant-btn-primary) {
  background: #0277bd;
  color: white;
  border-color: transparent;
}

:deep(.ant-btn-primary:hover) {
  background: #0288d1;
  color: white;
}

:deep(.ant-dropdown-menu-item) {
  color: #0277bd;
}

@media (max-width: 768px) {
  .site-title {
    display: none;
  }

  .logo-container {
    margin-right: 16px;
  }

  :deep(.custom-menu .ant-menu-item) {
    margin: 0 6px;
    padding: 0 14px;
    font-size: 14px;
  }

  .global-header {
    padding: 0 16px;
  }
}
</style>
