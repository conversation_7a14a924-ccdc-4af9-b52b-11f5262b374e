<template>
  <div class="home-page">
    <!-- 网站标题和介绍 -->
    <div class="hero-section">
      <div class="hero-content">
        <div class="title-container">
          <h1 class="main-title">
            一句话
            <span class="logo-icon">🤖</span>
            呈所想
          </h1>
          <p class="subtitle">与 AI 对话轻松创建应用和网站</p>
        </div>

        <!-- 用户提示词输入框 -->
        <div class="prompt-input-section">
          <a-input
            v-model:value="promptInput"
            placeholder="使用 NoCode 创建一个高效的小工具，帮我计算......"
            size="large"
            class="prompt-input"
            @pressEnter="handleCreateApp"
          >
            <template #suffix>
              <a-button
                type="primary"
                shape="circle"
                :loading="creating"
                @click="handleCreateApp"
                class="send-button"
              >
                <template #icon>
                  <SendOutlined />
                </template>
              </a-button>
            </template>
          </a-input>

          <!-- 快捷标签 -->
          <div class="quick-tags">
            <a-tag
              v-for="tag in quickTags"
              :key="tag"
              @click="handleTagClick(tag)"
              class="quick-tag"
            >
              {{ tag }}
            </a-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的应用列表 -->
    <div class="apps-section my-apps-section" v-if="loginUserStore.loginUser.id">
      <div class="section-header">
        <div class="section-title-container">
          <h2 class="section-title">
            <span class="title-icon">📱</span>
            我的应用
            <span class="app-count" v-if="myAppTotal > 0">({{ myAppTotal }})</span>
          </h2>
          <p class="section-subtitle">管理和查看您创建的所有应用</p>
        </div>
        <a-input-search
          v-model:value="myAppSearchText"
          placeholder="搜索我的应用"
          style="width: 300px"
          @search="searchMyApps"
          @change="handleMyAppSearchChange"
          class="search-input"
        />
      </div>

      <div v-if="myApps.length > 0" class="apps-content">
        <div class="apps-grid">
          <AppCard
            v-for="app in myApps"
            :key="app.id"
            :app="app"
            :show-author="false"
            @view-chat="handleViewChat"
            @view-work="handleViewWork"
            class="my-app-card"
          />
        </div>

        <div class="pagination-container" v-if="myAppTotal > 0">
          <a-pagination
            v-model:current="myAppPage"
            v-model:page-size="myAppPageSize"
            :total="myAppTotal"
            :show-size-changer="false"
            :show-quick-jumper="true"
            @change="loadMyApps"
          />
        </div>
      </div>

      <div v-else class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">🚀</div>
          <h3 class="empty-title">还没有应用</h3>
          <p class="empty-description">
            创建您的第一个AI应用，开启智能开发之旅
          </p>
          <a-button
            type="primary"
            size="large"
            @click="scrollToTop"
            class="create-app-button"
          >
            立即创建应用
          </a-button>
        </div>
      </div>
    </div>

    <!-- 精选应用列表 -->
    <div class="apps-section">
      <div class="section-header">
        <h2 class="section-title">精选应用</h2>
        <a-input-search
          v-model:value="goodAppSearchText"
          placeholder="搜索精选应用"
          style="width: 300px"
          @search="searchGoodApps"
          @change="handleGoodAppSearchChange"
        />
      </div>

      <div class="apps-grid" v-if="goodApps.length > 0">
        <AppCard
          v-for="app in goodApps"
          :key="app.id"
          :app="app"
          :show-author="true"
          @view-chat="handleViewChat"
          @view-work="handleViewWork"
        />
      </div>

      <a-empty v-else description="暂无精选应用" />

      <div class="pagination-container" v-if="goodAppTotal > 0">
        <a-pagination
          v-model:current="goodAppPage"
          v-model:page-size="goodAppPageSize"
          :total="goodAppTotal"
          :show-size-changer="false"
          :show-quick-jumper="true"
          @change="loadGoodApps"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SendOutlined } from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/loginUser'
import { addApp, listMyAppVoByPage, listGoodAppVoByPage } from '@/api/appController'
import AppCard from '@/components/AppCard.vue'

const router = useRouter()
const loginUserStore = useLoginUserStore()

// 提示词输入
const promptInput = ref('')
const creating = ref(false)

// 快捷标签
const quickTags = ref([
  '波普风电商页面',
  '企业网站',
  '电商运营后台',
  '暗黑话题社区'
])

// 我的应用
const myApps = ref<API.AppVO[]>([])
const myAppPage = ref(1)
const myAppPageSize = ref(20)
const myAppTotal = ref(0)
const myAppSearchText = ref('')

// 精选应用
const goodApps = ref<API.AppVO[]>([])
const goodAppPage = ref(1)
const goodAppPageSize = ref(20)
const goodAppTotal = ref(0)
const goodAppSearchText = ref('')

// 创建应用
const handleCreateApp = async () => {
  if (!promptInput.value.trim()) {
    message.warning('请输入应用描述')
    return
  }

  if (!loginUserStore.loginUser.id) {
    message.warning('请先登录')
    router.push('/user/login')
    return
  }

  creating.value = true
  try {
    const res = await addApp({
      initPrompt: promptInput.value.trim()
    })

    if (res.data.code === 0 && res.data.data) {
      message.success('应用创建成功')
      // 清空输入框
      promptInput.value = ''
      // 添加短暂延迟确保数据库已更新，然后跳转到对话页面
      setTimeout(() => {
        router.push(`/app/chat/${res.data.data}`)
      }, 500)
    } else {
      message.error('创建失败：' + res.data.message)
    }
  } catch (error) {
    message.error('创建失败，请稍后重试')
  } finally {
    creating.value = false
  }
}

// 快捷标签点击
const handleTagClick = (tag: string) => {
  promptInput.value = tag
}

// 加载我的应用
const loadMyApps = async (page = 1) => {
  if (!loginUserStore.loginUser.id) return

  try {
    const res = await listMyAppVoByPage({
      pageNum: page,
      pageSize: myAppPageSize.value,
      appName: myAppSearchText.value || undefined
    })

    if (res.data.code === 0 && res.data.data) {
      myApps.value = res.data.data.records || []
      myAppTotal.value = res.data.data.totalRow || 0
      myAppPage.value = page
    }
  } catch (error) {
    message.error('加载我的应用失败')
  }
}

// 加载精选应用
const loadGoodApps = async (page = 1) => {
  try {
    const res = await listGoodAppVoByPage({
      pageNum: page,
      pageSize: goodAppPageSize.value,
      appName: goodAppSearchText.value || undefined
    })

    if (res.data.code === 0 && res.data.data) {
      goodApps.value = res.data.data.records || []
      goodAppTotal.value = res.data.data.totalRow || 0
      goodAppPage.value = page
    }
  } catch (error) {
    message.error('加载精选应用失败')
  }
}

// 搜索我的应用
const searchMyApps = () => {
  myAppPage.value = 1
  loadMyApps(1)
}

// 搜索精选应用
const searchGoodApps = () => {
  goodAppPage.value = 1
  loadGoodApps(1)
}

// 搜索输入变化处理
const handleMyAppSearchChange = (e: any) => {
  if (!e.target.value) {
    searchMyApps()
  }
}

const handleGoodAppSearchChange = (e: any) => {
  if (!e.target.value) {
    searchGoodApps()
  }
}

// 查看对话
const handleViewChat = (app: API.AppVO) => {
  router.push(`/app/chat/${app.id}?view=1`)
}

// 查看作品
const handleViewWork = (app: API.AppVO) => {
  if (app.deployKey) {
    window.open(`http://localhost:8123/${app.deployKey}`, '_blank')
  }
}

// 滚动到顶部（用于空状态按钮）
const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 监听登录状态变化
watch(() => loginUserStore.loginUser.id, (newId) => {
  if (newId) {
    loadMyApps()
  } else {
    myApps.value = []
    myAppTotal.value = 0
  }
})

onMounted(() => {
  loadGoodApps()
  if (loginUserStore.loginUser.id) {
    loadMyApps()
  }
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  padding: 80px 0 120px;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.title-container {
  margin-bottom: 48px;
}

.main-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.logo-icon {
  font-size: 52px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.prompt-input-section {
  max-width: 600px;
  margin: 0 auto;
}

.prompt-input {
  border-radius: 50px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

:deep(.prompt-input .ant-input) {
  border: none;
  background: transparent;
  font-size: 16px;
  padding: 16px 24px;
}

:deep(.prompt-input .ant-input:focus) {
  box-shadow: none;
}

.send-button {
  background: #0277bd;
  border-color: #0277bd;
  margin-right: 8px;
}

.send-button:hover {
  background: #0288d1;
  border-color: #0288d1;
}

.quick-tags {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20px;
  padding: 6px 16px;
}

.quick-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.apps-section {
  background: white;
  padding: 48px 24px;
  margin: 0 auto;
  max-width: 1200px;
}

.apps-section:first-of-type {
  margin-top: -60px;
  border-radius: 24px 24px 0 0;
  position: relative;
  z-index: 1;
}

.my-apps-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.section-title-container {
  flex: 1;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
}

.app-count {
  font-size: 18px;
  color: #0277bd;
  font-weight: 500;
}

.section-subtitle {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.search-input {
  flex-shrink: 0;
  margin-left: 24px;
}

:deep(.search-input .ant-input-search) {
  border-radius: 8px;
}

:deep(.search-input .ant-input) {
  border-color: #d1d5db;
}

:deep(.search-input .ant-input:focus) {
  border-color: #0277bd;
  box-shadow: 0 0 0 2px rgba(2, 119, 189, 0.1);
}

.apps-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.my-app-card {
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.my-app-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #0277bd;
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 64px 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #334155;
  margin: 0 0 12px 0;
}

.empty-description {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.create-app-button {
  background: linear-gradient(135deg, #0277bd 0%, #0288d1 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(2, 119, 189, 0.3);
  transition: all 0.3s ease;
}

.create-app-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(2, 119, 189, 0.4);
  background: linear-gradient(135deg, #0288d1 0%, #039be5 100%);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

:deep(.ant-pagination) {
  text-align: center;
}

:deep(.ant-pagination .ant-pagination-item-active) {
  border-color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item:hover a) {
  color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-next:hover .ant-pagination-item-link,
.ant-pagination .ant-pagination-prev:hover .ant-pagination-item-link) {
  border-color: #0277bd;
  color: #0277bd;
}

@media (max-width: 768px) {
  .main-title {
    font-size: 36px;
    flex-direction: column;
    gap: 8px;
  }

  .logo-icon {
    font-size: 40px;
  }

  .hero-section {
    padding: 60px 0 80px;
  }

  .section-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .search-input {
    margin-left: 0;
  }

  .section-title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 28px;
  }

  .apps-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .apps-section {
    padding: 32px 16px;
  }

  .apps-content {
    padding: 16px;
  }

  .empty-state {
    padding: 48px 24px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-title {
    font-size: 20px;
  }

  .empty-description {
    font-size: 14px;
  }

  .create-app-button {
    width: 100%;
    height: 44px;
    font-size: 15px;
  }

  .quick-tags {
    gap: 8px;
  }

  .quick-tag {
    font-size: 12px;
    padding: 4px 12px;
  }
}
</style>
