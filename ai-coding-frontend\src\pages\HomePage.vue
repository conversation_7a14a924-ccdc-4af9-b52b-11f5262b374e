<template>
  <div class="home-page">
    <!-- 网站标题和介绍 -->
    <div class="hero-section">
      <div class="hero-content">
        <div class="title-container">
          <h1 class="main-title">
            一句话
            <span class="logo-icon">🤖</span>
            呈所想
          </h1>
          <p class="subtitle">与 AI 对话轻松创建应用和网站</p>
        </div>

        <!-- 用户提示词输入框 -->
        <div class="prompt-input-section">
          <a-input
            v-model:value="promptInput"
            placeholder="使用 NoCode 创建一个高效的小工具，帮我计算......"
            size="large"
            class="prompt-input"
            @pressEnter="handleCreateApp"
          >
            <template #suffix>
              <a-button
                type="primary"
                shape="circle"
                :loading="creating"
                @click="handleCreateApp"
                class="send-button"
              >
                <template #icon>
                  <SendOutlined />
                </template>
              </a-button>
            </template>
          </a-input>

          <!-- 快捷标签 -->
          <div class="quick-tags">
            <a-tag
              v-for="tag in quickTags"
              :key="tag"
              @click="handleTagClick(tag)"
              class="quick-tag"
            >
              {{ tag }}
            </a-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的应用列表 -->
    <div class="apps-section" v-if="loginUserStore.loginUser.id">
      <div class="section-header">
        <h2 class="section-title">我的应用</h2>
        <a-input-search
          v-model:value="myAppSearchText"
          placeholder="搜索我的应用"
          style="width: 300px"
          @search="searchMyApps"
          @change="handleMyAppSearchChange"
        />
      </div>

      <div class="apps-grid" v-if="myApps.length > 0">
        <AppCard
          v-for="app in myApps"
          :key="app.id"
          :app="app"
          :show-author="false"
          @view-chat="handleViewChat"
          @view-work="handleViewWork"
        />
      </div>

      <a-empty v-else description="暂无应用，快去创建一个吧！" />

      <div class="pagination-container" v-if="myAppTotal > 0">
        <a-pagination
          v-model:current="myAppPage"
          v-model:page-size="myAppPageSize"
          :total="myAppTotal"
          :show-size-changer="false"
          :show-quick-jumper="true"
          @change="loadMyApps"
        />
      </div>
    </div>

    <!-- 精选应用列表 -->
    <div class="apps-section">
      <div class="section-header">
        <h2 class="section-title">精选应用</h2>
        <a-input-search
          v-model:value="goodAppSearchText"
          placeholder="搜索精选应用"
          style="width: 300px"
          @search="searchGoodApps"
          @change="handleGoodAppSearchChange"
        />
      </div>

      <div class="apps-grid" v-if="goodApps.length > 0">
        <AppCard
          v-for="app in goodApps"
          :key="app.id"
          :app="app"
          :show-author="true"
          @view-chat="handleViewChat"
          @view-work="handleViewWork"
        />
      </div>

      <a-empty v-else description="暂无精选应用" />

      <div class="pagination-container" v-if="goodAppTotal > 0">
        <a-pagination
          v-model:current="goodAppPage"
          v-model:page-size="goodAppPageSize"
          :total="goodAppTotal"
          :show-size-changer="false"
          :show-quick-jumper="true"
          @change="loadGoodApps"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SendOutlined } from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/loginUser'
import { addApp, listMyAppVoByPage, listGoodAppVoByPage } from '@/api/appController'
import AppCard from '@/components/AppCard.vue'

const router = useRouter()
const loginUserStore = useLoginUserStore()

// 提示词输入
const promptInput = ref('')
const creating = ref(false)

// 快捷标签
const quickTags = ref([
  '波普风电商页面',
  '企业网站',
  '电商运营后台',
  '暗黑话题社区'
])

// 我的应用
const myApps = ref<API.AppVO[]>([])
const myAppPage = ref(1)
const myAppPageSize = ref(20)
const myAppTotal = ref(0)
const myAppSearchText = ref('')

// 精选应用
const goodApps = ref<API.AppVO[]>([])
const goodAppPage = ref(1)
const goodAppPageSize = ref(20)
const goodAppTotal = ref(0)
const goodAppSearchText = ref('')

// 创建应用
const handleCreateApp = async () => {
  if (!promptInput.value.trim()) {
    message.warning('请输入应用描述')
    return
  }

  if (!loginUserStore.loginUser.id) {
    message.warning('请先登录')
    router.push('/user/login')
    return
  }

  creating.value = true
  try {
    const res = await addApp({
      initPrompt: promptInput.value.trim()
    })

    if (res.data.code === 0 && res.data.data) {
      message.success('应用创建成功')
      // 清空输入框
      promptInput.value = ''
      // 添加短暂延迟确保数据库已更新，然后跳转到对话页面
      setTimeout(() => {
        router.push(`/app/chat/${res.data.data}`)
      }, 500)
    } else {
      message.error('创建失败：' + res.data.message)
    }
  } catch (error) {
    message.error('创建失败，请稍后重试')
  } finally {
    creating.value = false
  }
}

// 快捷标签点击
const handleTagClick = (tag: string) => {
  promptInput.value = tag
}

// 加载我的应用
const loadMyApps = async (page = 1) => {
  if (!loginUserStore.loginUser.id) return

  try {
    const res = await listMyAppVoByPage({
      pageNum: page,
      pageSize: myAppPageSize.value,
      appName: myAppSearchText.value || undefined
    })

    if (res.data.code === 0 && res.data.data) {
      myApps.value = res.data.data.records || []
      myAppTotal.value = res.data.data.totalRow || 0
      myAppPage.value = page
    }
  } catch (error) {
    message.error('加载我的应用失败')
  }
}

// 加载精选应用
const loadGoodApps = async (page = 1) => {
  try {
    const res = await listGoodAppVoByPage({
      pageNum: page,
      pageSize: goodAppPageSize.value,
      appName: goodAppSearchText.value || undefined
    })

    if (res.data.code === 0 && res.data.data) {
      goodApps.value = res.data.data.records || []
      goodAppTotal.value = res.data.data.totalRow || 0
      goodAppPage.value = page
    }
  } catch (error) {
    message.error('加载精选应用失败')
  }
}

// 搜索我的应用
const searchMyApps = () => {
  myAppPage.value = 1
  loadMyApps(1)
}

// 搜索精选应用
const searchGoodApps = () => {
  goodAppPage.value = 1
  loadGoodApps(1)
}

// 搜索输入变化处理
const handleMyAppSearchChange = (e: any) => {
  if (!e.target.value) {
    searchMyApps()
  }
}

const handleGoodAppSearchChange = (e: any) => {
  if (!e.target.value) {
    searchGoodApps()
  }
}

// 查看对话
const handleViewChat = (app: API.AppVO) => {
  router.push(`/app/chat/${app.id}?view=1`)
}

// 查看作品
const handleViewWork = (app: API.AppVO) => {
  if (app.deployKey) {
    window.open(`http://localhost:8123/${app.deployKey}`, '_blank')
  }
}

// 监听登录状态变化
watch(() => loginUserStore.loginUser.id, (newId) => {
  if (newId) {
    loadMyApps()
  } else {
    myApps.value = []
    myAppTotal.value = 0
  }
})

onMounted(() => {
  loadGoodApps()
  if (loginUserStore.loginUser.id) {
    loadMyApps()
  }
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  padding: 80px 0 120px;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.title-container {
  margin-bottom: 48px;
}

.main-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.logo-icon {
  font-size: 52px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.prompt-input-section {
  max-width: 600px;
  margin: 0 auto;
}

.prompt-input {
  border-radius: 50px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

:deep(.prompt-input .ant-input) {
  border: none;
  background: transparent;
  font-size: 16px;
  padding: 16px 24px;
}

:deep(.prompt-input .ant-input:focus) {
  box-shadow: none;
}

.send-button {
  background: #0277bd;
  border-color: #0277bd;
  margin-right: 8px;
}

.send-button:hover {
  background: #0288d1;
  border-color: #0288d1;
}

.quick-tags {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.quick-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20px;
  padding: 6px 16px;
}

.quick-tag:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.apps-section {
  background: white;
  padding: 48px 24px;
  margin: 0 auto;
  max-width: 1200px;
}

.apps-section:first-of-type {
  margin-top: -60px;
  border-radius: 24px 24px 0 0;
  position: relative;
  z-index: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.section-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

:deep(.ant-pagination) {
  text-align: center;
}

:deep(.ant-pagination .ant-pagination-item-active) {
  border-color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item-active a) {
  color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item:hover) {
  border-color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-item:hover a) {
  color: #0277bd;
}

:deep(.ant-pagination .ant-pagination-next:hover .ant-pagination-item-link,
.ant-pagination .ant-pagination-prev:hover .ant-pagination-item-link) {
  border-color: #0277bd;
  color: #0277bd;
}

@media (max-width: 768px) {
  .main-title {
    font-size: 36px;
    flex-direction: column;
    gap: 8px;
  }

  .logo-icon {
    font-size: 40px;
  }

  .hero-section {
    padding: 60px 0 80px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .apps-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .apps-section {
    padding: 32px 16px;
  }

  .quick-tags {
    gap: 8px;
  }

  .quick-tag {
    font-size: 12px;
    padding: 4px 12px;
  }
}
</style>
