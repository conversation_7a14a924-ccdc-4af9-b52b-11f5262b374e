<template>
  <footer class="global-footer">
    <div class="footer-content">
      © 2023 AI零代码开发平台 版权所有 | 隐私政策 | 服务条款
    </div>
  </footer>
</template>

<script setup lang="ts">
// 空的 setup 函数，可以根据需要添加逻辑
</script>

<style scoped>
.global-footer {
  width: 100%;
  padding: 24px 16px;
  text-align: center;
  color: #999;
  position: fixed;
  bottom: 0;
  left: 0;
  border-top: 1px solid #ddd;
  z-index: 10;
}

.footer-content {
  font-size: 14px;
  line-height: 1.5;
}

</style>
